import React, { useEffect, useState } from 'react';
import { FaChevronDown, FaChevronUp } from 'react-icons/fa';
import { Link } from 'react-router-dom';
import api from '../api';
import Banner from './Banner';
import './FAQPage.css';

const FAQPage = () => {
  const [openIndices, setOpenIndices] = useState([]);
  const [faqs, setFaqs] = useState([]);

  // Fetch FAQ data from backend API
  useEffect(() => {
    const fetchFAQs = async () => {
      try {
        const response = await api.get('/faq');
        const transformedFaqs = response.data.FAQList.map(faq => ({
          question: faq.Question,
          answer: faq.Answer
        }));
        setFaqs(transformedFaqs);
      } catch (error) {
        console.error('Error fetching FAQs:', error);
      }
    };

    fetchFAQs();
  }, []);

  const handleToggle = idx => {
    setOpenIndices(prev =>
      prev.includes(idx)
        ? prev.filter(i => i !== idx)
        : [...prev, idx]
    );
  };

  return (
    <>
      <Banner />
      <div className="faq-page-outer">
        <div className="faq-title-bg">
          <div className="faq-title-row">
            <span className="faq-title-main">FAQ</span>
            <span className="faq-title-sub">- FREQUENTLY ASKED QUESTIONS</span>
          </div>
        </div>
        <div className="faq-breadcrumb">
          <Link to="/home" className="faq-home-link no-underline">Home page</Link> / FAQ
        </div>
        <div className="faq-list">
          {faqs.map((faq, idx) => (
            <div className={`faq-item-card${openIndices.includes(idx) ? ' open' : ''}`} key={idx}>
              <div
                className="faq-question-row"
                onClick={() => handleToggle(idx)}
              >
                <span className="faq-chevron">
                  {openIndices.includes(idx) ? <FaChevronUp /> : <FaChevronDown />}
                </span>
                <span className="faq-question"><strong>{faq.question}</strong></span>
              </div>
              <div
                className={`faq-answer-animated${openIndices.includes(idx) ? ' show' : ''}`}
                style={{ maxHeight: openIndices.includes(idx) ? '200px' : '0px' }}
              >
                <div className="faq-answer-inner">{faq.answer}</div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </>
  );
};

export default FAQPage;
