import React, { useEffect, useState } from 'react';
import { FaChevronDown, FaChevronUp } from 'react-icons/fa';
import { Link } from 'react-router-dom';
import Banner from './Banner';
import './FAQPage.css';

const FAQPage = () => {
  const [openIndices, setOpenIndices] = useState([]);
  const [faqs, setFaqs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch FAQ data from backend API
  const fetchFAQs = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/faq/');

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // Transform backend data format to match frontend expectations
      const transformedFaqs = data.FAQList.map(faq => ({
        question: faq.Question,
        answer: faq.Answer
      }));

      setFaqs(transformedFaqs);
    } catch (error) {
      console.error('Error fetching FAQs:', error);
      setError('Failed to load FAQ data. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Fetch FAQs on component mount
  useEffect(() => {
    fetchFAQs();
  }, []);

  const handleToggle = idx => {
    setOpenIndices(prev =>
      prev.includes(idx)
        ? prev.filter(i => i !== idx)
        : [...prev, idx]
    );
  };

  return (
    <>
      <Banner />
      <div className="faq-page-outer">
        <div className="faq-title-bg">
          <div className="faq-title-row">
            <span className="faq-title-main">FAQ</span>
            <span className="faq-title-sub">- FREQUENTLY ASKED QUESTIONS</span>
          </div>
        </div>
        <div className="faq-breadcrumb">
          <Link to="/home" className="faq-home-link no-underline">Home page</Link> / FAQ
        </div>
        <div className="faq-list">
          {loading ? (
            <div className="faq-loading">
              <p>Loading FAQ...</p>
            </div>
          ) : error ? (
            <div className="faq-error">
              <p>{error}</p>
              <button onClick={fetchFAQs} className="retry-button">
                Retry
              </button>
            </div>
          ) : (
            faqs.map((faq, idx) => (
              <div className={`faq-item-card${openIndices.includes(idx) ? ' open' : ''}`} key={idx}>
                <div
                  className="faq-question-row"
                  onClick={() => handleToggle(idx)}
                >
                  <span className="faq-chevron">
                    {openIndices.includes(idx) ? <FaChevronUp /> : <FaChevronDown />}
                  </span>
                  <span className="faq-question"><strong>{faq.question}</strong></span>
                </div>
                <div
                  className={`faq-answer-animated${openIndices.includes(idx) ? ' show' : ''}`}
                  style={{ maxHeight: openIndices.includes(idx) ? '200px' : '0px' }}
                >
                  <div className="faq-answer-inner">{faq.answer}</div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </>
  );
};

export default FAQPage;
